#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minecraft服务器BAT文件编辑器
支持图形界面配置所有启动参数并保存到本地
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import json
import re
import subprocess
import platform
from pathlib import Path

# 尝试导入拖放支持
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    HAS_DND = True
except ImportError:
    HAS_DND = False

class MinecraftBatEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("Minecraft服务器BAT文件编辑器")
        self.root.geometry("800x700")

        # 配置文件路径
        self.config_file = "minecraft_config.json"

        # Minecraft版本信息
        self.minecraft_versions = {
            "1.20+": {
                "name": "1.20及以上版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200",
                "min_java": "Java 17+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "1.17-1.19": {
                "name": "1.17-1.19版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled",
                "min_java": "Java 16+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "1.13-1.16": {
                "name": "1.13-1.16版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "1.7-1.12": {
                "name": "1.7-1.12版本 (首次引入nogui)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "gui"
            },
            "1.6及以下": {
                "name": "1.6及以下版本 (仅GUI模式)",
                "supports_nogui": False,
                "supports_gui": True,
                "default_jvm_args": "",
                "min_java": "Java 6+",
                "port_param": "-p",
                "recommended_gui": "gui"
            },
            "forge": {
                "name": "Forge服务器 (模组)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -Dfml.queryResult=confirm",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "gui"
            },
            "fabric": {
                "name": "Fabric服务器 (轻量模组)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "paper": {
                "name": "Paper服务器 (高性能)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "spigot": {
                "name": "Spigot服务器 (插件)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            }
        }

        # 默认配置
        self.default_config = {
            "java_path": "java",
            "jar_file": "server.jar",
            "minecraft_version": "1.20+",
            "min_memory": "1G",
            "max_memory": "4G",
            "server_port": "25565",
            "gui_mode": "nogui",
            "additional_jvm_args": "",
            "additional_args": "",
            "pause_on_exit": True,
            "output_file": "start_server.bat",
            "auto_detect_version": True,
            "show_startup_info": True,
            "show_shutdown_info": True,
            "custom_startup_message": "",
            "custom_shutdown_message": ""
        }

        self.config = self.load_config()
        self.create_widgets()

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        row = 0

        # 标题
        title_label = ttk.Label(main_frame, text="Minecraft服务器启动配置",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1

        # Java路径
        java_frame = ttk.Frame(main_frame)
        java_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        java_frame.columnconfigure(1, weight=1)

        ttk.Label(java_frame, text="Java路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.java_path_var = tk.StringVar(value=self.config["java_path"])
        self.java_entry = ttk.Entry(java_frame, textvariable=self.java_path_var, width=40)
        self.java_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))

        # Java选择按钮
        java_button_frame = ttk.Frame(java_frame)
        java_button_frame.grid(row=0, column=2, pady=5, padx=(5, 0))

        ttk.Button(java_button_frame, text="浏览",
                  command=self.browse_java).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(java_button_frame, text="搜索",
                  command=self.search_java).pack(side=tk.LEFT)

        # Java版本选择下拉框
        ttk.Label(java_frame, text="检测到的Java版本:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.java_versions_var = tk.StringVar()
        self.java_combo = ttk.Combobox(java_frame, textvariable=self.java_versions_var,
                                     state="readonly", width=60)
        self.java_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.java_combo.bind('<<ComboboxSelected>>', self.on_java_selected)

        # 添加拖放提示和功能
        self.setup_drag_drop(self.java_entry, "java")
        self.setup_windows_drag_drop(self.java_entry, "java")

        # 初始搜索Java版本
        self.search_java_versions()
        row += 1

        # JAR文件
        ttk.Label(main_frame, text="服务器JAR文件:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.jar_file_var = tk.StringVar(value=self.config["jar_file"])
        self.jar_entry = ttk.Entry(main_frame, textvariable=self.jar_file_var, width=50)
        self.jar_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        ttk.Button(main_frame, text="浏览",
                  command=self.browse_jar).grid(row=row, column=2, pady=5, padx=(5, 0))

        # 添加拖放提示和功能
        self.setup_drag_drop(self.jar_entry, "jar")
        self.setup_windows_drag_drop(self.jar_entry, "jar")
        row += 1

        # 版本选择框架
        version_frame = ttk.LabelFrame(main_frame, text="Minecraft版本设置", padding="10")
        version_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        version_frame.columnconfigure(1, weight=1)

        ttk.Label(version_frame, text="服务器类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.version_var = tk.StringVar(value=self.config["minecraft_version"])
        version_combo = ttk.Combobox(version_frame, textvariable=self.version_var,
                                   values=list(self.minecraft_versions.keys()),
                                   state="readonly", width=20)
        version_combo.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(5, 0))
        version_combo.bind('<<ComboboxSelected>>', self.on_version_change)

        self.auto_detect_var = tk.BooleanVar(value=self.config.get("auto_detect_version", True))
        ttk.Checkbutton(version_frame, text="自动检测版本 (基于JAR文件名)",
                       variable=self.auto_detect_var,
                       command=self.on_auto_detect_change).grid(row=1, column=0, columnspan=2,
                                                               sticky=tk.W, pady=5)

        # 版本信息显示
        self.version_info_var = tk.StringVar()
        self.version_info_label = ttk.Label(version_frame, textvariable=self.version_info_var,
                                          foreground="blue", font=("Arial", 9))
        self.version_info_label.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        row += 1

        # 内存设置框架
        memory_frame = ttk.LabelFrame(main_frame, text="内存设置", padding="10")
        memory_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        memory_frame.columnconfigure(1, weight=1)
        memory_frame.columnconfigure(3, weight=1)

        ttk.Label(memory_frame, text="最小内存:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.min_memory_var = tk.StringVar(value=self.config["min_memory"])
        ttk.Entry(memory_frame, textvariable=self.min_memory_var, width=10).grid(
            row=0, column=1, sticky=tk.W, pady=5, padx=(5, 10))

        ttk.Label(memory_frame, text="最大内存:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.max_memory_var = tk.StringVar(value=self.config["max_memory"])
        ttk.Entry(memory_frame, textvariable=self.max_memory_var, width=10).grid(
            row=0, column=3, sticky=tk.W, pady=5, padx=(5, 0))

        row += 1

        # 服务器设置框架
        server_frame = ttk.LabelFrame(main_frame, text="服务器设置", padding="10")
        server_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        server_frame.columnconfigure(1, weight=1)

        ttk.Label(server_frame, text="服务器端口:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.server_port_var = tk.StringVar(value=self.config["server_port"])
        ttk.Entry(server_frame, textvariable=self.server_port_var, width=10).grid(
            row=0, column=1, sticky=tk.W, pady=5, padx=(5, 0))

        # GUI选项
        ttk.Label(server_frame, text="GUI模式:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.gui_mode_var = tk.StringVar(value=self.config.get("gui_mode", "nogui"))
        self.gui_mode_frame = ttk.Frame(server_frame)
        self.gui_mode_frame.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(5, 0))

        self.gui_radio_nogui = ttk.Radiobutton(self.gui_mode_frame, text="无GUI (nogui)",
                                             variable=self.gui_mode_var, value="nogui",
                                             command=self.update_preview)
        self.gui_radio_nogui.pack(side=tk.LEFT, padx=(0, 10))

        self.gui_radio_gui = ttk.Radiobutton(self.gui_mode_frame, text="有GUI",
                                           variable=self.gui_mode_var, value="gui",
                                           command=self.update_preview)
        self.gui_radio_gui.pack(side=tk.LEFT)

        self.pause_var = tk.BooleanVar(value=self.config["pause_on_exit"])
        ttk.Checkbutton(server_frame, text="退出时暂停 (pause)",
                       variable=self.pause_var).grid(row=2, column=0, columnspan=2,
                                                    sticky=tk.W, pady=5)

        row += 1

        # 启动/关闭信息设置框架
        message_frame = ttk.LabelFrame(main_frame, text="启动/关闭信息设置", padding="10")
        message_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        message_frame.columnconfigure(1, weight=1)

        # 启动信息选项
        self.show_startup_var = tk.BooleanVar(value=self.config.get("show_startup_info", True))
        ttk.Checkbutton(message_frame, text="显示启动信息",
                       variable=self.show_startup_var,
                       command=self.update_preview).grid(row=0, column=0, sticky=tk.W, pady=5)

        ttk.Label(message_frame, text="自定义启动消息:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.startup_message_var = tk.StringVar(value=self.config.get("custom_startup_message", ""))
        startup_entry = ttk.Entry(message_frame, textvariable=self.startup_message_var, width=50)
        startup_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        startup_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 关闭信息选项
        self.show_shutdown_var = tk.BooleanVar(value=self.config.get("show_shutdown_info", True))
        ttk.Checkbutton(message_frame, text="显示关闭信息",
                       variable=self.show_shutdown_var,
                       command=self.update_preview).grid(row=2, column=0, sticky=tk.W, pady=5)

        ttk.Label(message_frame, text="自定义关闭消息:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.shutdown_message_var = tk.StringVar(value=self.config.get("custom_shutdown_message", ""))
        shutdown_entry = ttk.Entry(message_frame, textvariable=self.shutdown_message_var, width=50)
        shutdown_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        shutdown_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        row += 1

        # 高级设置框架
        advanced_frame = ttk.LabelFrame(main_frame, text="高级设置", padding="10")
        advanced_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        advanced_frame.columnconfigure(0, weight=1)

        ttk.Label(advanced_frame, text="额外JVM参数:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.jvm_args_var = tk.StringVar(value=self.config["additional_jvm_args"])
        ttk.Entry(advanced_frame, textvariable=self.jvm_args_var, width=60).grid(
            row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(advanced_frame, text="额外启动参数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.additional_args_var = tk.StringVar(value=self.config["additional_args"])
        ttk.Entry(advanced_frame, textvariable=self.additional_args_var, width=60).grid(
            row=3, column=0, sticky=(tk.W, tk.E), pady=5)

        row += 1

        # 输出文件设置
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        output_frame.columnconfigure(1, weight=1)

        ttk.Label(output_frame, text="输出文件名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.output_file_var = tk.StringVar(value=self.config["output_file"])
        ttk.Entry(output_frame, textvariable=self.output_file_var, width=40).grid(
            row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(output_frame, text="选择位置",
                  command=self.browse_output).grid(row=0, column=2, pady=5)

        row += 1

        # 预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="BAT文件预览", padding="10")
        preview_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)

        self.preview_text = tk.Text(preview_frame, height=8, width=80, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=scrollbar.set)

        self.preview_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        row += 1

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="更新预览",
                  command=self.update_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置",
                  command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="加载配置",
                  command=self.load_config_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="生成BAT文件",
                  command=self.generate_bat).pack(side=tk.LEFT, padx=5)

        # 初始预览
        self.update_version_info()
        self.update_gui_options()
        self.update_preview()

    def setup_drag_drop(self, widget, file_type):
        """设置拖放功能"""
        if HAS_DND:
            # 如果有tkinterdnd2支持，使用完整的拖放功能
            try:
                widget.drop_target_register(DND_FILES)
                if file_type == "java":
                    widget.dnd_bind('<<Drop>>', self.on_java_drop)
                elif file_type == "jar":
                    widget.dnd_bind('<<Drop>>', self.on_jar_drop)
            except Exception as e:
                print(f"拖放设置失败: {e}")
                self.setup_basic_drag_drop(widget, file_type)
        else:
            # 使用基本的拖放功能（Windows原生支持）
            self.setup_basic_drag_drop(widget, file_type)

    def setup_basic_drag_drop(self, widget, file_type):
        """设置基本拖放功能（不依赖外部库）"""
        def on_drop(event):
            # 获取拖放的文件路径
            files = event.data
            if files:
                # 处理文件路径
                if files.startswith('{') and files.endswith('}'):
                    files = files[1:-1]  # 移除大括号

                file_path = files.strip()

                if file_type == "java":
                    if file_path.lower().endswith(('.exe', '.jar')) or 'java' in file_path.lower():
                        self.java_path_var.set(file_path)
                        self.update_preview()
                    else:
                        messagebox.showwarning("文件类型错误", "请拖放Java可执行文件")
                elif file_type == "jar":
                    if file_path.lower().endswith('.jar'):
                        self.jar_file_var.set(file_path)
                        if self.auto_detect_var.get():
                            self.detect_version_from_jar(file_path)
                        self.update_preview()
                    else:
                        messagebox.showwarning("文件类型错误", "请拖放JAR文件")

        # 绑定拖放事件
        widget.bind('<Button-1>', lambda e: widget.focus_set())

        # 添加右键提示
        def show_drag_tip(event):
            tip_msg = f"拖放提示:\n"
            if file_type == "java":
                tip_msg += "• 直接拖放Java可执行文件到此输入框\n"
                tip_msg += "• 或使用'浏览'/'搜索'按钮选择"
            else:
                tip_msg += "• 直接拖放JAR文件到此输入框\n"
                tip_msg += "• 或使用'浏览'按钮选择"

            if not HAS_DND:
                tip_msg += "\n\n要启用完整拖放功能，请运行:\npip install tkinterdnd2"

            messagebox.showinfo("拖放功能", tip_msg)

        widget.bind('<Button-3>', show_drag_tip)

    def setup_windows_drag_drop(self, widget, file_type):
        """设置Windows原生拖放功能"""
        try:
            # 简化的拖放支持 - 使用tkinter的内置功能
            def on_drag_enter(event):
                widget.config(bg='lightblue')
                return 'copy'

            def on_drag_leave(event):
                widget.config(bg='white')

            def on_drop(event):
                widget.config(bg='white')
                # 尝试从剪贴板获取文件路径
                try:
                    file_path = self.root.clipboard_get()
                    if os.path.exists(file_path):
                        self.handle_dropped_file(file_path, file_type)
                except:
                    # 如果剪贴板方法失败，显示手动输入提示
                    result = messagebox.askquestion("拖放文件",
                        f"检测到拖放操作。\n请手动输入{file_type}文件路径，或使用浏览按钮选择文件。\n\n是否打开文件选择对话框？")
                    if result == 'yes':
                        if file_type == "java":
                            self.browse_java()
                        elif file_type == "jar":
                            self.browse_jar()

            # 绑定拖放事件（简化版本）
            widget.bind('<Button-1>', lambda e: widget.focus_set())
            widget.bind('<Double-Button-1>', on_drop)

            print(f"为 {file_type} 输入框启用了简化拖放支持（双击触发）")

        except Exception as e:
            print(f"拖放设置失败: {e}")

    def handle_dropped_file(self, file_path, file_type):
        """处理拖放的文件"""
        try:
            if file_type == "java":
                if file_path.lower().endswith(('.exe', '.jar')) or 'java' in file_path.lower():
                    self.java_path_var.set(file_path)
                    self.update_preview()
                    messagebox.showinfo("设置成功", f"已设置Java路径: {os.path.basename(file_path)}")
                else:
                    messagebox.showwarning("文件类型错误", "请选择Java可执行文件")
            elif file_type == "jar":
                if file_path.lower().endswith('.jar'):
                    self.jar_file_var.set(file_path)
                    if self.auto_detect_var.get():
                        self.detect_version_from_jar(file_path)
                    self.update_preview()
                    messagebox.showinfo("设置成功", f"已设置JAR文件: {os.path.basename(file_path)}")
                else:
                    messagebox.showwarning("文件类型错误", "请选择JAR文件")
        except Exception as e:
            print(f"文件处理错误: {e}")
            messagebox.showerror("错误", f"处理文件时出错: {e}")



    def on_java_drop(self, event):
        """处理Java文件拖放"""
        files = self.parse_drop_files(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith(('.exe', '.jar')) or 'java' in file_path.lower():
                self.java_path_var.set(file_path)
                self.update_preview()
            else:
                messagebox.showwarning("文件类型错误", "请拖放Java可执行文件 (.exe) 或包含'java'的文件")

    def on_jar_drop(self, event):
        """处理JAR文件拖放"""
        files = self.parse_drop_files(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith('.jar'):
                self.jar_file_var.set(file_path)
                if self.auto_detect_var.get():
                    self.detect_version_from_jar(file_path)
                self.update_preview()
            else:
                messagebox.showwarning("文件类型错误", "请拖放JAR文件 (.jar)")

    def parse_drop_files(self, data):
        """解析拖放的文件数据"""
        # 处理拖放数据，可能包含多个文件
        files = []
        if data:
            # 移除大括号并分割文件路径
            data = data.strip('{}')
            files = [f.strip() for f in data.split('}') if f.strip()]
        return files

    def browse_java(self):
        """浏览选择Java可执行文件"""
        filename = filedialog.askopenfilename(
            title="选择Java可执行文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.java_path_var.set(filename)
            self.update_preview()

    def browse_jar(self):
        """浏览选择服务器JAR文件"""
        filename = filedialog.askopenfilename(
            title="选择Minecraft服务器JAR文件",
            filetypes=[("JAR文件", "*.jar"), ("所有文件", "*.*")]
        )
        if filename:
            self.jar_file_var.set(filename)
            if self.auto_detect_var.get():
                self.detect_version_from_jar(filename)
            self.update_preview()

    def browse_output(self):
        """选择输出BAT文件位置"""
        filename = filedialog.asksaveasfilename(
            title="保存BAT文件",
            defaultextension=".bat",
            filetypes=[("批处理文件", "*.bat"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)

    def update_preview(self):
        """更新BAT文件预览"""
        bat_content = self.generate_bat_content()
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, bat_content)

    def generate_bat_content(self):
        """生成BAT文件内容"""
        java_path = self.java_path_var.get().strip()
        jar_file = self.jar_file_var.get().strip()
        min_memory = self.min_memory_var.get().strip()
        max_memory = self.max_memory_var.get().strip()
        server_port = self.server_port_var.get().strip()
        jvm_args = self.jvm_args_var.get().strip()
        additional_args = self.additional_args_var.get().strip()
        version_key = self.version_var.get()
        gui_mode = self.gui_mode_var.get()

        # 获取版本信息
        version_info = self.minecraft_versions.get(version_key, self.minecraft_versions["1.20+"])

        # 构建命令行
        cmd_parts = []

        # 添加注释头
        bat_content = "@echo off\n"
        bat_content += "title Minecraft Server\n"

        # 启动信息
        if self.show_startup_var.get():
            custom_startup = self.startup_message_var.get().strip()
            if custom_startup:
                bat_content += f"echo {custom_startup}\n"
            else:
                bat_content += f"echo Starting {version_info['name']}...\n"
                bat_content += f"echo Required: {version_info['min_java']}\n"
            bat_content += "echo.\n"

        bat_content += "\n"

        # Java路径
        if java_path:
            cmd_parts.append(f'"{java_path}"')
        else:
            cmd_parts.append("java")

        # 内存设置
        if min_memory:
            cmd_parts.append(f"-Xms{min_memory}")
        if max_memory:
            cmd_parts.append(f"-Xmx{max_memory}")

        # 额外JVM参数
        if jvm_args:
            cmd_parts.append(jvm_args)

        # JAR文件
        cmd_parts.append("-jar")
        if jar_file:
            cmd_parts.append(f'"{jar_file}"')
        else:
            cmd_parts.append("server.jar")

        # 服务器端口 (根据版本使用不同的参数格式)
        if server_port and server_port != "25565":
            port_param = version_info.get("port_param", "--port")
            if port_param == "-p":
                cmd_parts.append(f"-p {server_port}")
            else:
                cmd_parts.append(f"--port {server_port}")

        # GUI设置 (根据版本支持情况)
        if gui_mode == "nogui" and version_info["supports_nogui"]:
            cmd_parts.append("nogui")
        elif gui_mode == "gui" and not version_info["supports_gui"]:
            # 如果版本不支持GUI但用户选择了GUI，添加警告注释
            bat_content += "REM Warning: This version does not support GUI mode\n"

        # 额外参数
        if additional_args:
            cmd_parts.append(additional_args)

        # 组合命令
        bat_content += " ".join(cmd_parts) + "\n\n"

        # 添加关闭信息和暂停
        if self.show_shutdown_var.get() or self.pause_var.get():
            bat_content += "echo.\n"

            if self.show_shutdown_var.get():
                custom_shutdown = self.shutdown_message_var.get().strip()
                if custom_shutdown:
                    bat_content += f"echo {custom_shutdown}\n"
                else:
                    bat_content += "echo Server stopped.\n"

            if self.pause_var.get():
                bat_content += "echo Press any key to exit...\n"
                bat_content += "pause >nul\n"

        return bat_content

    def save_config(self):
        """保存当前配置到JSON文件"""
        config = {
            "java_path": self.java_path_var.get(),
            "jar_file": self.jar_file_var.get(),
            "minecraft_version": self.version_var.get(),
            "min_memory": self.min_memory_var.get(),
            "max_memory": self.max_memory_var.get(),
            "server_port": self.server_port_var.get(),
            "gui_mode": self.gui_mode_var.get(),
            "additional_jvm_args": self.jvm_args_var.get(),
            "additional_args": self.additional_args_var.get(),
            "pause_on_exit": self.pause_var.get(),
            "output_file": self.output_file_var.get(),
            "auto_detect_version": self.auto_detect_var.get(),
            "show_startup_info": self.show_startup_var.get(),
            "show_shutdown_info": self.show_shutdown_var.get(),
            "custom_startup_message": self.startup_message_var.get(),
            "custom_shutdown_message": self.shutdown_message_var.get()
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("成功", f"配置已保存到 {self.config_file}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """从JSON文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return {**self.default_config, **config}
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")
                return self.default_config
        return self.default_config

    def load_config_file(self):
        """从文件加载配置"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新界面
                self.java_path_var.set(config.get("java_path", ""))
                self.jar_file_var.set(config.get("jar_file", ""))
                self.version_var.set(config.get("minecraft_version", "1.20+"))
                self.min_memory_var.set(config.get("min_memory", "1G"))
                self.max_memory_var.set(config.get("max_memory", "4G"))
                self.server_port_var.set(config.get("server_port", "25565"))
                self.gui_mode_var.set(config.get("gui_mode", "nogui"))
                self.jvm_args_var.set(config.get("additional_jvm_args", ""))
                self.additional_args_var.set(config.get("additional_args", ""))
                self.pause_var.set(config.get("pause_on_exit", True))
                self.output_file_var.set(config.get("output_file", "start_server.bat"))
                self.auto_detect_var.set(config.get("auto_detect_version", True))
                self.show_startup_var.set(config.get("show_startup_info", True))
                self.show_shutdown_var.set(config.get("show_shutdown_info", True))
                self.startup_message_var.set(config.get("custom_startup_message", ""))
                self.shutdown_message_var.set(config.get("custom_shutdown_message", ""))

                self.update_version_info()
                self.update_gui_options()
                self.update_preview()
                messagebox.showinfo("成功", "配置加载成功")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def generate_bat(self):
        """生成并保存BAT文件"""
        output_file = self.output_file_var.get().strip()
        if not output_file:
            messagebox.showerror("错误", "请指定输出文件名")
            return

        try:
            bat_content = self.generate_bat_content()

            # 确保文件扩展名为.bat
            if not output_file.lower().endswith('.bat'):
                output_file += '.bat'

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(bat_content)

            messagebox.showinfo("成功", f"BAT文件已生成: {output_file}")

            # 询问是否打开文件所在目录
            if messagebox.askyesno("打开目录", "是否打开文件所在目录?"):
                import subprocess
                import platform

                file_path = os.path.abspath(output_file)
                if platform.system() == "Windows":
                    subprocess.run(f'explorer /select,"{file_path}"', shell=True)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", "-R", file_path])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(file_path)])

        except Exception as e:
            messagebox.showerror("错误", f"生成BAT文件失败: {str(e)}")

    def detect_version_from_jar(self, jar_path):
        """从JAR文件名自动检测Minecraft版本"""
        jar_name = os.path.basename(jar_path).lower()

        # 检测服务器类型
        if "paper" in jar_name:
            detected_version = "paper"
        elif "spigot" in jar_name:
            detected_version = "spigot"
        elif "forge" in jar_name:
            detected_version = "forge"
        elif "fabric" in jar_name:
            detected_version = "fabric"
        else:
            # 检测版本号
            import re
            version_patterns = [
                (r"1\.20", "1.20+"),
                (r"1\.1[789]", "1.17-1.19"),
                (r"1\.1[3456]", "1.13-1.16"),
                (r"1\.[789]|1\.1[012]", "1.7-1.12"),
                (r"1\.[0-6]", "1.6及以下")
            ]

            detected_version = "1.20+"  # 默认
            for pattern, version in version_patterns:
                if re.search(pattern, jar_name):
                    detected_version = version
                    break

        self.version_var.set(detected_version)
        self.on_version_change()

    def on_version_change(self, event=None):
        """版本改变时的回调"""
        # event参数用于tkinter回调，可能为None
        self.update_version_info()
        self.update_gui_options()
        self.update_jvm_args()
        self.update_preview()

    def on_auto_detect_change(self):
        """自动检测选项改变时的回调"""
        if self.auto_detect_var.get() and self.jar_file_var.get():
            self.detect_version_from_jar(self.jar_file_var.get())

    def update_version_info(self):
        """更新版本信息显示"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]
            info_text = f"{version_info['name']} - 需要 {version_info['min_java']}"
            self.version_info_var.set(info_text)

    def update_gui_options(self):
        """根据版本更新GUI选项的可用性"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]

            # 更新GUI选项状态
            if version_info["supports_nogui"]:
                self.gui_radio_nogui.config(state="normal")
            else:
                self.gui_radio_nogui.config(state="disabled")
                if self.gui_mode_var.get() == "nogui":
                    self.gui_mode_var.set("gui")

            if version_info["supports_gui"]:
                self.gui_radio_gui.config(state="normal")
            else:
                self.gui_radio_gui.config(state="disabled")
                if self.gui_mode_var.get() == "gui":
                    self.gui_mode_var.set("nogui")

            # 如果当前GUI模式为空或不合适，设置为推荐模式
            current_mode = self.gui_mode_var.get()
            recommended_mode = version_info.get("recommended_gui", "nogui")

            if not current_mode or (current_mode == "nogui" and not version_info["supports_nogui"]) or (current_mode == "gui" and not version_info["supports_gui"]):
                self.gui_mode_var.set(recommended_mode)

    def update_jvm_args(self):
        """根据版本更新推荐的JVM参数"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]
            current_args = self.jvm_args_var.get().strip()

            # 如果当前JVM参数为空，使用推荐参数
            if not current_args and version_info["default_jvm_args"]:
                self.jvm_args_var.set(version_info["default_jvm_args"])

    def search_java_versions(self):
        """搜索系统中可用的Java版本"""
        print("开始搜索Java安装...")
        java_installations = self.find_java_installations()

        if java_installations:
            # 格式化显示选项
            display_options = []
            self.java_paths = {}  # 存储显示文本到路径的映射

            for java_info in java_installations:
                display_text = f"{java_info['version']} - {java_info['path']}"
                display_options.append(display_text)
                self.java_paths[display_text] = java_info['path']
                print(f"找到Java: {display_text}")

            self.java_combo['values'] = display_options

            # 如果当前路径在列表中，选中它
            current_path = self.java_path_var.get()
            for display_text, path in self.java_paths.items():
                if path == current_path:
                    self.java_versions_var.set(display_text)
                    break

            print(f"总共找到 {len(java_installations)} 个Java安装")
        else:
            self.java_combo['values'] = ["未找到Java安装 - 点击搜索重试"]
            print("未找到任何Java安装")

    def find_java_installations(self):
        """查找系统中的Java安装"""
        java_installations = []

        try:
            # 检查PATH中的java
            try:
                result = subprocess.run(['java', '-version'],
                                      capture_output=True, text=True,
                                      stderr=subprocess.STDOUT, timeout=10)
                if result.returncode == 0:
                    version = self.parse_java_version(result.stdout)
                    java_installations.append({
                        'path': 'java',
                        'version': f"系统PATH中的Java {version}",
                        'type': 'system'
                    })
            except:
                pass

            # 根据操作系统搜索常见安装位置
            system = platform.system().lower()

            if system == 'windows':
                java_installations.extend(self.find_windows_java())
            elif system == 'linux':
                java_installations.extend(self.find_linux_java())
            elif system == 'darwin':  # macOS
                java_installations.extend(self.find_macos_java())

        except Exception as e:
            print(f"搜索Java时出错: {e}")

        return java_installations

    def find_windows_java(self):
        """在Windows系统中查找Java"""
        java_installations = []

        # 常见的Java安装路径
        search_paths = [
            "C:/Program Files/Java",
            "C:/Program Files (x86)/Java",
            "C:/Program Files/Eclipse Adoptium",
            "C:/Program Files/Eclipse Foundation",
            "C:/Program Files/Amazon Corretto",
            "C:/Program Files/Zulu",
            "C:/Program Files/Microsoft",  # Microsoft OpenJDK
            "C:/Program Files/AdoptOpenJDK",
            "C:/Program Files/OpenJDK",
            "C:/Program Files/BellSoft",  # Liberica JDK
            "C:/Program Files/Azul",      # Azul Zulu
            os.path.expanduser("~/AppData/Local/Programs/Java"),
            os.path.expanduser("~/AppData/Local/Microsoft/WindowsApps"),  # Microsoft Store Java
            "D:/Program Files/Java",     # 其他盘符
            "E:/Program Files/Java",
        ]

        print("搜索Windows Java安装路径...")
        for base_path in search_paths:
            print(f"检查路径: {base_path}")
            if os.path.exists(base_path):
                try:
                    items = os.listdir(base_path)
                    print(f"  找到子目录: {items}")
                    for item in items:
                        java_dir = os.path.join(base_path, item)
                        if os.path.isdir(java_dir):
                            # 检查多个可能的java.exe位置
                            possible_java_paths = [
                                os.path.join(java_dir, "bin", "java.exe"),
                                os.path.join(java_dir, "java.exe"),
                                os.path.join(java_dir, "jre", "bin", "java.exe"),
                            ]

                            for java_exe in possible_java_paths:
                                if os.path.exists(java_exe):
                                    print(f"  找到Java可执行文件: {java_exe}")
                                    version = self.get_java_version(java_exe)
                                    if version:
                                        java_installations.append({
                                            'path': java_exe,
                                            'version': f"Java {version} ({item})",
                                            'type': 'installed'
                                        })
                                        print(f"  版本: Java {version}")
                                    break
                except Exception as e:
                    print(f"  搜索 {base_path} 时出错: {e}")
                    continue
            else:
                print(f"  路径不存在: {base_path}")

        # 尝试通过注册表查找Java（Windows特有）
        try:
            java_installations.extend(self.find_java_from_registry())
        except Exception as e:
            print(f"注册表搜索失败: {e}")

        return java_installations

    def find_java_from_registry(self):
        """通过Windows注册表查找Java"""
        java_installations = []

        try:
            import winreg

            # 搜索注册表中的Java安装信息
            registry_paths = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JavaSoft\Java Runtime Environment"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JavaSoft\Java Development Kit"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JavaSoft\JDK"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Eclipse Adoptium\JDK"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Eclipse Foundation\JDK"),
            ]

            for hkey, subkey in registry_paths:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        i = 0
                        while True:
                            try:
                                version_key = winreg.EnumKey(key, i)
                                with winreg.OpenKey(key, version_key) as version_reg:
                                    try:
                                        java_home, _ = winreg.QueryValueEx(version_reg, "JavaHome")
                                        java_exe = os.path.join(java_home, "bin", "java.exe")
                                        if os.path.exists(java_exe):
                                            java_installations.append({
                                                'path': java_exe,
                                                'version': f"Java {version_key} (注册表)",
                                                'type': 'registry'
                                            })
                                            print(f"从注册表找到: Java {version_key} at {java_exe}")
                                    except FileNotFoundError:
                                        pass
                                i += 1
                            except OSError:
                                break
                except FileNotFoundError:
                    continue

        except ImportError:
            print("winreg模块不可用，跳过注册表搜索")
        except Exception as e:
            print(f"注册表搜索出错: {e}")

        return java_installations

    def find_linux_java(self):
        """在Linux系统中查找Java"""
        java_installations = []

        # 常见的Java安装路径
        search_paths = [
            "/usr/lib/jvm",
            "/usr/java",
            "/opt/java",
            "/opt/jdk",
            "/usr/local/java",
            os.path.expanduser("~/java"),
        ]

        for base_path in search_paths:
            if os.path.exists(base_path):
                try:
                    for item in os.listdir(base_path):
                        java_dir = os.path.join(base_path, item)
                        if os.path.isdir(java_dir):
                            java_exe = os.path.join(java_dir, "bin", "java")
                            if os.path.exists(java_exe):
                                version = self.get_java_version(java_exe)
                                if version:
                                    java_installations.append({
                                        'path': java_exe,
                                        'version': f"Java {version} ({item})",
                                        'type': 'installed'
                                    })
                except:
                    continue

        return java_installations

    def find_macos_java(self):
        """在macOS系统中查找Java"""
        java_installations = []

        # macOS特有的Java路径
        search_paths = [
            "/Library/Java/JavaVirtualMachines",
            "/System/Library/Java/JavaVirtualMachines",
            os.path.expanduser("~/Library/Java/JavaVirtualMachines"),
        ]

        for base_path in search_paths:
            if os.path.exists(base_path):
                try:
                    for item in os.listdir(base_path):
                        java_dir = os.path.join(base_path, item, "Contents", "Home")
                        if os.path.isdir(java_dir):
                            java_exe = os.path.join(java_dir, "bin", "java")
                            if os.path.exists(java_exe):
                                version = self.get_java_version(java_exe)
                                if version:
                                    java_installations.append({
                                        'path': java_exe,
                                        'version': f"Java {version} ({item})",
                                        'type': 'installed'
                                    })
                except:
                    continue

        return java_installations

    def get_java_version(self, java_path):
        """获取指定Java可执行文件的版本"""
        try:
            result = subprocess.run([java_path, '-version'],
                                  capture_output=True, text=True,
                                  stderr=subprocess.STDOUT, timeout=10)
            if result.returncode == 0:
                return self.parse_java_version(result.stdout)
        except:
            pass
        return None

    def parse_java_version(self, version_output):
        """解析Java版本输出"""
        try:
            # 匹配版本号模式
            patterns = [
                r'version "(\d+\.\d+\.\d+[^"]*)"',  # Java 8及以下
                r'version "(\d+[^"]*)"',            # Java 9+
                r'openjdk version "([^"]*)"',       # OpenJDK
            ]

            for pattern in patterns:
                match = re.search(pattern, version_output)
                if match:
                    version = match.group(1)
                    # 简化版本显示
                    if version.startswith('1.8'):
                        return "8"
                    elif version.startswith('1.'):
                        return version[2:]
                    else:
                        return version.split('.')[0]
        except:
            pass
        return "未知版本"

    def search_java(self):
        """手动触发Java搜索"""
        self.search_java_versions()
        messagebox.showinfo("搜索完成", f"找到 {len(self.java_combo['values'])} 个Java安装")

    def on_java_selected(self, event=None):
        """Java版本选择回调"""
        selected = self.java_versions_var.get()
        if selected and selected in self.java_paths:
            self.java_path_var.set(self.java_paths[selected])
            self.update_preview()


def main():
    """主函数"""
    if HAS_DND:
        # 如果有拖放支持，使用TkinterDnD
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()

    MinecraftBatEditor(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("minecraft.ico")
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
