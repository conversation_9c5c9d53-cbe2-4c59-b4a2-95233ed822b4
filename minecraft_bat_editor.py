#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minecraft服务器BAT文件编辑器
支持图形界面配置所有启动参数并保存到本地
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import json

class MinecraftBatEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("Minecraft服务器BAT文件编辑器")
        self.root.geometry("800x700")

        # 配置文件路径
        self.config_file = "minecraft_config.json"

        # Minecraft版本信息
        self.minecraft_versions = {
            "1.20+": {
                "name": "1.20及以上版本",
                "supports_nogui": True,
                "supports_gui": False,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200",
                "min_java": "Java 17+",
                "port_param": "--port"
            },
            "1.17-1.19": {
                "name": "1.17-1.19版本",
                "supports_nogui": True,
                "supports_gui": False,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled",
                "min_java": "Java 16+",
                "port_param": "--port"
            },
            "1.13-1.16": {
                "name": "1.13-1.16版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port"
            },
            "1.7-1.12": {
                "name": "1.7-1.12版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "",
                "min_java": "Java 8+",
                "port_param": "--port"
            },
            "1.6及以下": {
                "name": "1.6及以下版本",
                "supports_nogui": False,
                "supports_gui": True,
                "default_jvm_args": "",
                "min_java": "Java 6+",
                "port_param": "-p"
            },
            "forge": {
                "name": "Forge服务器",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -Dfml.queryResult=confirm",
                "min_java": "Java 8+",
                "port_param": "--port"
            },
            "fabric": {
                "name": "Fabric服务器",
                "supports_nogui": True,
                "supports_gui": False,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port"
            },
            "paper": {
                "name": "Paper服务器",
                "supports_nogui": True,
                "supports_gui": False,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC",
                "min_java": "Java 8+",
                "port_param": "--port"
            },
            "spigot": {
                "name": "Spigot服务器",
                "supports_nogui": True,
                "supports_gui": False,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port"
            }
        }

        # 默认配置
        self.default_config = {
            "java_path": "java",
            "jar_file": "server.jar",
            "minecraft_version": "1.20+",
            "min_memory": "1G",
            "max_memory": "4G",
            "server_port": "25565",
            "gui_mode": "nogui",
            "additional_jvm_args": "",
            "additional_args": "",
            "pause_on_exit": True,
            "output_file": "start_server.bat",
            "auto_detect_version": True
        }

        self.config = self.load_config()
        self.create_widgets()

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        row = 0

        # 标题
        title_label = ttk.Label(main_frame, text="Minecraft服务器启动配置",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1

        # Java路径
        ttk.Label(main_frame, text="Java路径:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.java_path_var = tk.StringVar(value=self.config["java_path"])
        java_entry = ttk.Entry(main_frame, textvariable=self.java_path_var, width=50)
        java_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        ttk.Button(main_frame, text="浏览",
                  command=self.browse_java).grid(row=row, column=2, pady=5, padx=(5, 0))
        row += 1

        # JAR文件
        ttk.Label(main_frame, text="服务器JAR文件:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.jar_file_var = tk.StringVar(value=self.config["jar_file"])
        jar_entry = ttk.Entry(main_frame, textvariable=self.jar_file_var, width=50)
        jar_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        ttk.Button(main_frame, text="浏览",
                  command=self.browse_jar).grid(row=row, column=2, pady=5, padx=(5, 0))
        row += 1

        # 版本选择框架
        version_frame = ttk.LabelFrame(main_frame, text="Minecraft版本设置", padding="10")
        version_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        version_frame.columnconfigure(1, weight=1)

        ttk.Label(version_frame, text="服务器类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.version_var = tk.StringVar(value=self.config["minecraft_version"])
        version_combo = ttk.Combobox(version_frame, textvariable=self.version_var,
                                   values=list(self.minecraft_versions.keys()),
                                   state="readonly", width=20)
        version_combo.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(5, 0))
        version_combo.bind('<<ComboboxSelected>>', self.on_version_change)

        self.auto_detect_var = tk.BooleanVar(value=self.config.get("auto_detect_version", True))
        ttk.Checkbutton(version_frame, text="自动检测版本 (基于JAR文件名)",
                       variable=self.auto_detect_var,
                       command=self.on_auto_detect_change).grid(row=1, column=0, columnspan=2,
                                                               sticky=tk.W, pady=5)

        # 版本信息显示
        self.version_info_var = tk.StringVar()
        self.version_info_label = ttk.Label(version_frame, textvariable=self.version_info_var,
                                          foreground="blue", font=("Arial", 9))
        self.version_info_label.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        row += 1

        # 内存设置框架
        memory_frame = ttk.LabelFrame(main_frame, text="内存设置", padding="10")
        memory_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        memory_frame.columnconfigure(1, weight=1)
        memory_frame.columnconfigure(3, weight=1)

        ttk.Label(memory_frame, text="最小内存:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.min_memory_var = tk.StringVar(value=self.config["min_memory"])
        ttk.Entry(memory_frame, textvariable=self.min_memory_var, width=10).grid(
            row=0, column=1, sticky=tk.W, pady=5, padx=(5, 10))

        ttk.Label(memory_frame, text="最大内存:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.max_memory_var = tk.StringVar(value=self.config["max_memory"])
        ttk.Entry(memory_frame, textvariable=self.max_memory_var, width=10).grid(
            row=0, column=3, sticky=tk.W, pady=5, padx=(5, 0))

        row += 1

        # 服务器设置框架
        server_frame = ttk.LabelFrame(main_frame, text="服务器设置", padding="10")
        server_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        server_frame.columnconfigure(1, weight=1)

        ttk.Label(server_frame, text="服务器端口:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.server_port_var = tk.StringVar(value=self.config["server_port"])
        ttk.Entry(server_frame, textvariable=self.server_port_var, width=10).grid(
            row=0, column=1, sticky=tk.W, pady=5, padx=(5, 0))

        # GUI选项
        ttk.Label(server_frame, text="GUI模式:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.gui_mode_var = tk.StringVar(value=self.config.get("gui_mode", "nogui"))
        self.gui_mode_frame = ttk.Frame(server_frame)
        self.gui_mode_frame.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(5, 0))

        self.gui_radio_nogui = ttk.Radiobutton(self.gui_mode_frame, text="无GUI (nogui)",
                                             variable=self.gui_mode_var, value="nogui",
                                             command=self.update_preview)
        self.gui_radio_nogui.pack(side=tk.LEFT, padx=(0, 10))

        self.gui_radio_gui = ttk.Radiobutton(self.gui_mode_frame, text="有GUI",
                                           variable=self.gui_mode_var, value="gui",
                                           command=self.update_preview)
        self.gui_radio_gui.pack(side=tk.LEFT)

        self.pause_var = tk.BooleanVar(value=self.config["pause_on_exit"])
        ttk.Checkbutton(server_frame, text="退出时暂停 (pause)",
                       variable=self.pause_var).grid(row=2, column=0, columnspan=2,
                                                    sticky=tk.W, pady=5)

        row += 1

        # 高级设置框架
        advanced_frame = ttk.LabelFrame(main_frame, text="高级设置", padding="10")
        advanced_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        advanced_frame.columnconfigure(0, weight=1)

        ttk.Label(advanced_frame, text="额外JVM参数:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.jvm_args_var = tk.StringVar(value=self.config["additional_jvm_args"])
        ttk.Entry(advanced_frame, textvariable=self.jvm_args_var, width=60).grid(
            row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(advanced_frame, text="额外启动参数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.additional_args_var = tk.StringVar(value=self.config["additional_args"])
        ttk.Entry(advanced_frame, textvariable=self.additional_args_var, width=60).grid(
            row=3, column=0, sticky=(tk.W, tk.E), pady=5)

        row += 1

        # 输出文件设置
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        output_frame.columnconfigure(1, weight=1)

        ttk.Label(output_frame, text="输出文件名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.output_file_var = tk.StringVar(value=self.config["output_file"])
        ttk.Entry(output_frame, textvariable=self.output_file_var, width=40).grid(
            row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(output_frame, text="选择位置",
                  command=self.browse_output).grid(row=0, column=2, pady=5)

        row += 1

        # 预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="BAT文件预览", padding="10")
        preview_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)

        self.preview_text = tk.Text(preview_frame, height=8, width=80, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=scrollbar.set)

        self.preview_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        row += 1

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="更新预览",
                  command=self.update_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置",
                  command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="加载配置",
                  command=self.load_config_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="生成BAT文件",
                  command=self.generate_bat).pack(side=tk.LEFT, padx=5)

        # 初始预览
        self.update_version_info()
        self.update_gui_options()
        self.update_preview()

    def browse_java(self):
        """浏览选择Java可执行文件"""
        filename = filedialog.askopenfilename(
            title="选择Java可执行文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.java_path_var.set(filename)
            self.update_preview()

    def browse_jar(self):
        """浏览选择服务器JAR文件"""
        filename = filedialog.askopenfilename(
            title="选择Minecraft服务器JAR文件",
            filetypes=[("JAR文件", "*.jar"), ("所有文件", "*.*")]
        )
        if filename:
            self.jar_file_var.set(filename)
            if self.auto_detect_var.get():
                self.detect_version_from_jar(filename)
            self.update_preview()

    def browse_output(self):
        """选择输出BAT文件位置"""
        filename = filedialog.asksaveasfilename(
            title="保存BAT文件",
            defaultextension=".bat",
            filetypes=[("批处理文件", "*.bat"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)

    def update_preview(self):
        """更新BAT文件预览"""
        bat_content = self.generate_bat_content()
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, bat_content)

    def generate_bat_content(self):
        """生成BAT文件内容"""
        java_path = self.java_path_var.get().strip()
        jar_file = self.jar_file_var.get().strip()
        min_memory = self.min_memory_var.get().strip()
        max_memory = self.max_memory_var.get().strip()
        server_port = self.server_port_var.get().strip()
        jvm_args = self.jvm_args_var.get().strip()
        additional_args = self.additional_args_var.get().strip()
        version_key = self.version_var.get()
        gui_mode = self.gui_mode_var.get()

        # 获取版本信息
        version_info = self.minecraft_versions.get(version_key, self.minecraft_versions["1.20+"])

        # 构建命令行
        cmd_parts = []

        # 添加注释头
        bat_content = "@echo off\n"
        bat_content += "title Minecraft Server\n"
        bat_content += f"echo Starting {version_info['name']}...\n"
        bat_content += f"echo Required: {version_info['min_java']}\n"
        bat_content += "echo.\n\n"

        # Java路径
        if java_path:
            cmd_parts.append(f'"{java_path}"')
        else:
            cmd_parts.append("java")

        # 内存设置
        if min_memory:
            cmd_parts.append(f"-Xms{min_memory}")
        if max_memory:
            cmd_parts.append(f"-Xmx{max_memory}")

        # 额外JVM参数
        if jvm_args:
            cmd_parts.append(jvm_args)

        # JAR文件
        cmd_parts.append("-jar")
        if jar_file:
            cmd_parts.append(f'"{jar_file}"')
        else:
            cmd_parts.append("server.jar")

        # 服务器端口 (根据版本使用不同的参数格式)
        if server_port and server_port != "25565":
            port_param = version_info.get("port_param", "--port")
            if port_param == "-p":
                cmd_parts.append(f"-p {server_port}")
            else:
                cmd_parts.append(f"--port {server_port}")

        # GUI设置 (根据版本支持情况)
        if gui_mode == "nogui" and version_info["supports_nogui"]:
            cmd_parts.append("nogui")
        elif gui_mode == "gui" and not version_info["supports_gui"]:
            # 如果版本不支持GUI但用户选择了GUI，添加警告注释
            bat_content += "REM Warning: This version does not support GUI mode\n"

        # 额外参数
        if additional_args:
            cmd_parts.append(additional_args)

        # 组合命令
        bat_content += " ".join(cmd_parts) + "\n\n"

        # 添加暂停
        if self.pause_var.get():
            bat_content += "echo.\n"
            bat_content += "echo Server stopped. Press any key to exit...\n"
            bat_content += "pause >nul\n"

        return bat_content

    def save_config(self):
        """保存当前配置到JSON文件"""
        config = {
            "java_path": self.java_path_var.get(),
            "jar_file": self.jar_file_var.get(),
            "minecraft_version": self.version_var.get(),
            "min_memory": self.min_memory_var.get(),
            "max_memory": self.max_memory_var.get(),
            "server_port": self.server_port_var.get(),
            "gui_mode": self.gui_mode_var.get(),
            "additional_jvm_args": self.jvm_args_var.get(),
            "additional_args": self.additional_args_var.get(),
            "pause_on_exit": self.pause_var.get(),
            "output_file": self.output_file_var.get(),
            "auto_detect_version": self.auto_detect_var.get()
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("成功", f"配置已保存到 {self.config_file}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """从JSON文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return {**self.default_config, **config}
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")
                return self.default_config
        return self.default_config

    def load_config_file(self):
        """从文件加载配置"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新界面
                self.java_path_var.set(config.get("java_path", ""))
                self.jar_file_var.set(config.get("jar_file", ""))
                self.version_var.set(config.get("minecraft_version", "1.20+"))
                self.min_memory_var.set(config.get("min_memory", "1G"))
                self.max_memory_var.set(config.get("max_memory", "4G"))
                self.server_port_var.set(config.get("server_port", "25565"))
                self.gui_mode_var.set(config.get("gui_mode", "nogui"))
                self.jvm_args_var.set(config.get("additional_jvm_args", ""))
                self.additional_args_var.set(config.get("additional_args", ""))
                self.pause_var.set(config.get("pause_on_exit", True))
                self.output_file_var.set(config.get("output_file", "start_server.bat"))
                self.auto_detect_var.set(config.get("auto_detect_version", True))

                self.update_version_info()
                self.update_gui_options()
                self.update_preview()
                messagebox.showinfo("成功", "配置加载成功")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def generate_bat(self):
        """生成并保存BAT文件"""
        output_file = self.output_file_var.get().strip()
        if not output_file:
            messagebox.showerror("错误", "请指定输出文件名")
            return

        try:
            bat_content = self.generate_bat_content()

            # 确保文件扩展名为.bat
            if not output_file.lower().endswith('.bat'):
                output_file += '.bat'

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(bat_content)

            messagebox.showinfo("成功", f"BAT文件已生成: {output_file}")

            # 询问是否打开文件所在目录
            if messagebox.askyesno("打开目录", "是否打开文件所在目录?"):
                import subprocess
                import platform

                file_path = os.path.abspath(output_file)
                if platform.system() == "Windows":
                    subprocess.run(f'explorer /select,"{file_path}"', shell=True)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", "-R", file_path])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(file_path)])

        except Exception as e:
            messagebox.showerror("错误", f"生成BAT文件失败: {str(e)}")

    def detect_version_from_jar(self, jar_path):
        """从JAR文件名自动检测Minecraft版本"""
        jar_name = os.path.basename(jar_path).lower()

        # 检测服务器类型
        if "paper" in jar_name:
            detected_version = "paper"
        elif "spigot" in jar_name:
            detected_version = "spigot"
        elif "forge" in jar_name:
            detected_version = "forge"
        elif "fabric" in jar_name:
            detected_version = "fabric"
        else:
            # 检测版本号
            import re
            version_patterns = [
                (r"1\.20", "1.20+"),
                (r"1\.1[789]", "1.17-1.19"),
                (r"1\.1[3456]", "1.13-1.16"),
                (r"1\.[789]|1\.1[012]", "1.7-1.12"),
                (r"1\.[0-6]", "1.6及以下")
            ]

            detected_version = "1.20+"  # 默认
            for pattern, version in version_patterns:
                if re.search(pattern, jar_name):
                    detected_version = version
                    break

        self.version_var.set(detected_version)
        self.on_version_change()

    def on_version_change(self, event=None):
        """版本改变时的回调"""
        # event参数用于tkinter回调，可能为None
        self.update_version_info()
        self.update_gui_options()
        self.update_jvm_args()
        self.update_preview()

    def on_auto_detect_change(self):
        """自动检测选项改变时的回调"""
        if self.auto_detect_var.get() and self.jar_file_var.get():
            self.detect_version_from_jar(self.jar_file_var.get())

    def update_version_info(self):
        """更新版本信息显示"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]
            info_text = f"{version_info['name']} - 需要 {version_info['min_java']}"
            self.version_info_var.set(info_text)

    def update_gui_options(self):
        """根据版本更新GUI选项的可用性"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]

            # 更新GUI选项状态
            if version_info["supports_nogui"]:
                self.gui_radio_nogui.config(state="normal")
            else:
                self.gui_radio_nogui.config(state="disabled")
                if self.gui_mode_var.get() == "nogui":
                    self.gui_mode_var.set("gui")

            if version_info["supports_gui"]:
                self.gui_radio_gui.config(state="normal")
            else:
                self.gui_radio_gui.config(state="disabled")
                if self.gui_mode_var.get() == "gui":
                    self.gui_mode_var.set("nogui")

    def update_jvm_args(self):
        """根据版本更新推荐的JVM参数"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]
            current_args = self.jvm_args_var.get().strip()

            # 如果当前JVM参数为空，使用推荐参数
            if not current_args and version_info["default_jvm_args"]:
                self.jvm_args_var.set(version_info["default_jvm_args"])


def main():
    """主函数"""
    root = tk.Tk()
    MinecraftBatEditor(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("minecraft.ico")
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
