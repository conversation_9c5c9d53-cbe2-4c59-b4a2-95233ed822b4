# Minecraft服务器BAT文件编辑器

这是一个用Python编写的图形界面工具，用于创建和编辑Minecraft服务器的启动BAT文件。

## 功能特性

- 🎮 **图形界面操作** - 简单易用的GUI界面
- ⚙️ **完整配置选项** - 支持所有常用的Minecraft服务器启动参数
- 🎯 **版本智能识别** - 自动检测Minecraft版本并应用对应参数
- 💾 **配置保存/加载** - 可以保存和加载配置文件，方便重复使用
- 👀 **实时预览** - 实时预览生成的BAT文件内容
- 📁 **文件浏览** - 内置文件浏览器，方便选择Java和JAR文件
- 🖱️ **拖放支持** - 支持直接拖放Java和JAR文件到输入框
- 🔧 **高级设置** - 支持自定义JVM参数和额外启动参数
- 📝 **自定义消息** - 可自定义启动和关闭时的显示信息

## 支持的配置项

### 版本设置
- **服务器类型** - 支持原版、Forge、Fabric、Paper、Spigot等
- **自动检测** - 根据JAR文件名自动识别版本和类型
- **版本兼容性** - 自动应用版本特定的参数和限制

### 基本设置
- **Java路径** - 指定Java可执行文件的路径 (支持拖放)
- **JAR文件** - 指定Minecraft服务器JAR文件 (支持拖放)
- **最小内存** - 设置JVM最小内存分配 (例如: 1G, 512M)
- **最大内存** - 设置JVM最大内存分配 (例如: 4G, 8G)
- **服务器端口** - 设置服务器监听端口 (默认: 25565)

### 服务器选项
- **GUI模式** - 根据版本支持情况选择有GUI或无GUI模式
- **退出时暂停** - 在服务器停止后暂停，方便查看错误信息

### 启动/关闭信息
- **显示启动信息** - 控制是否显示启动时的信息
- **自定义启动消息** - 设置自定义的启动欢迎信息
- **显示关闭信息** - 控制是否显示关闭时的信息
- **自定义关闭消息** - 设置自定义的关闭提示信息

### 高级设置
- **额外JVM参数** - 自定义JVM启动参数 (根据版本自动推荐)
- **额外启动参数** - 自定义服务器启动参数

## 使用方法

### 1. 运行程序
```bash
python minecraft_bat_editor.py
```

### 2. 配置参数
- 在界面中填写各项配置
- 可以使用"浏览"按钮选择Java和JAR文件
- **拖放功能**: 直接将Java或JAR文件拖放到对应的输入框中
- 程序会自动检测JAR文件的版本和类型
- 实时预览窗口会显示生成的BAT文件内容

### 3. 启用拖放功能 (可选)
如果要使用拖放功能，需要安装额外的库：
```bash
pip install tkinterdnd2
```
或者运行提供的安装脚本：
```bash
install_dragdrop.bat
```

### 3. 保存配置
- 点击"保存配置"按钮保存当前设置到JSON文件
- 下次可以使用"加载配置"按钮快速恢复设置

### 4. 生成BAT文件
- 点击"生成BAT文件"按钮创建启动脚本
- 程序会询问是否打开文件所在目录

## 配置文件格式

配置文件采用JSON格式，示例：

```json
{
    "java_path": "C:/Program Files/Java/jdk-17/bin/java.exe",
    "jar_file": "paper-1.20.1-196.jar",
    "minecraft_version": "paper",
    "min_memory": "2G",
    "max_memory": "8G",
    "server_port": "25565",
    "gui_mode": "nogui",
    "additional_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200",
    "additional_args": "",
    "pause_on_exit": true,
    "output_file": "start_server.bat",
    "auto_detect_version": true,
    "show_startup_info": true,
    "show_shutdown_info": true,
    "custom_startup_message": "欢迎来到我的Minecraft服务器！",
    "custom_shutdown_message": "服务器已关闭，感谢游玩！"
}
```

## 生成的BAT文件示例

```batch
@echo off
title Minecraft Server
echo 欢迎来到我的Minecraft服务器！
echo.

"C:/Program Files/Java/jdk-17/bin/java.exe" -Xms2G -Xmx8G -XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -jar "paper-1.20.1-196.jar" nogui

echo.
echo 服务器已关闭，感谢游玩！
echo Press any key to exit...
pause >nul
```

## 系统要求

- Python 3.6+
- tkinter (通常随Python一起安装)
- Windows/Linux/macOS

## 支持的Minecraft版本

### 原版服务器
- **1.20+**: 需要Java 17+，支持nogui，推荐G1GC
- **1.17-1.19**: 需要Java 16+，支持nogui，推荐G1GC
- **1.13-1.16**: 需要Java 8+，支持GUI和nogui
- **1.7-1.12**: 需要Java 8+，支持GUI和nogui
- **1.6及以下**: 需要Java 6+，仅支持GUI，端口参数为-p

### 模组服务器
- **Forge**: 支持GUI和nogui，自动添加Forge特定参数
- **Fabric**: 仅支持nogui，轻量级模组加载器
- **Paper**: 高性能服务器，仅支持nogui，优化的JVM参数
- **Spigot**: 插件服务器，仅支持nogui

## 常见问题

### Q: 如何选择合适的内存设置？
A: 一般建议：
- 小型服务器 (1-10人): 2G-4G
- 中型服务器 (10-50人): 4G-8G
- 大型服务器 (50+人): 8G+

### Q: 推荐的JVM参数有哪些？
A: 程序会根据选择的版本自动推荐合适的JVM参数：
- **1.20+**: `-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200`
- **Paper**: 包含额外的性能优化参数
- **Forge**: 包含`-Dfml.queryResult=confirm`等Forge特定参数

### Q: 拖放功能不工作怎么办？
A:
1. 运行`install_dragdrop.bat`安装拖放支持库
2. 或手动执行`pip install tkinterdnd2`
3. 如果仍然不工作，可以右键点击输入框查看提示

### Q: 如何设置开机自启动？
A: 生成BAT文件后，可以：
1. 将BAT文件添加到Windows启动文件夹
2. 或者创建Windows服务
3. 或者使用任务计划程序

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
