# Minecraft服务器BAT文件编辑器

这是一个用Python编写的图形界面工具，用于创建和编辑Minecraft服务器的启动BAT文件。

## 功能特性

- 🎮 **图形界面操作** - 简单易用的GUI界面
- ⚙️ **完整配置选项** - 支持所有常用的Minecraft服务器启动参数
- 💾 **配置保存/加载** - 可以保存和加载配置文件，方便重复使用
- 👀 **实时预览** - 实时预览生成的BAT文件内容
- 📁 **文件浏览** - 内置文件浏览器，方便选择Java和JAR文件
- 🔧 **高级设置** - 支持自定义JVM参数和额外启动参数

## 支持的配置项

### 基本设置
- **Java路径** - 指定Java可执行文件的路径
- **JAR文件** - 指定Minecraft服务器JAR文件
- **最小内存** - 设置JVM最小内存分配 (例如: 1G, 512M)
- **最大内存** - 设置JVM最大内存分配 (例如: 4G, 8G)
- **服务器端口** - 设置服务器监听端口 (默认: 25565)

### 服务器选项
- **无GUI模式** - 启用nogui参数，适合服务器环境
- **退出时暂停** - 在服务器停止后暂停，方便查看错误信息

### 高级设置
- **额外JVM参数** - 自定义JVM启动参数
- **额外启动参数** - 自定义服务器启动参数

## 使用方法

### 1. 运行程序
```bash
python minecraft_bat_editor.py
```

### 2. 配置参数
- 在界面中填写各项配置
- 可以使用"浏览"按钮选择Java和JAR文件
- 实时预览窗口会显示生成的BAT文件内容

### 3. 保存配置
- 点击"保存配置"按钮保存当前设置到JSON文件
- 下次可以使用"加载配置"按钮快速恢复设置

### 4. 生成BAT文件
- 点击"生成BAT文件"按钮创建启动脚本
- 程序会询问是否打开文件所在目录

## 配置文件格式

配置文件采用JSON格式，示例：

```json
{
    "java_path": "C:/Program Files/Java/jdk-17/bin/java.exe",
    "jar_file": "server.jar",
    "min_memory": "2G",
    "max_memory": "8G",
    "server_port": "25565",
    "nogui": true,
    "additional_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled",
    "additional_args": "",
    "pause_on_exit": true,
    "output_file": "start_server.bat"
}
```

## 生成的BAT文件示例

```batch
@echo off
title Minecraft Server
echo Starting Minecraft Server...
echo.

"C:/Program Files/Java/jdk-17/bin/java.exe" -Xms2G -Xmx8G -XX:+UseG1GC -XX:+ParallelRefProcEnabled -jar "server.jar" nogui

echo.
echo Server stopped. Press any key to exit...
pause >nul
```

## 系统要求

- Python 3.6+
- tkinter (通常随Python一起安装)
- Windows/Linux/macOS

## 常见问题

### Q: 如何选择合适的内存设置？
A: 一般建议：
- 小型服务器 (1-10人): 2G-4G
- 中型服务器 (10-50人): 4G-8G  
- 大型服务器 (50+人): 8G+

### Q: 推荐的JVM参数有哪些？
A: 对于现代Minecraft服务器，推荐使用：
```
-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC
```

### Q: 如何设置开机自启动？
A: 生成BAT文件后，可以：
1. 将BAT文件添加到Windows启动文件夹
2. 或者创建Windows服务
3. 或者使用任务计划程序

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
